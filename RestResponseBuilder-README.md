# RestResponseBuilder Documentation

## Overview

The `RestResponseBuilder` is a utility class that provides a fluent API for creating standardized REST API responses in your Spring Boot application. It works seamlessly with the existing `ResponseStructure<T>` class and provides consistent response formatting across your entire API.

## Features

- **Fluent API**: Easy-to-use builder pattern for creating responses
- **Standardized Structure**: All responses follow the same format using `ResponseStructure<T>`
- **HTTP Status Integration**: Automatically sets appropriate HTTP status codes
- **Type Safety**: Generic support for different response data types
- **Error Handling**: Built-in methods for common error scenarios
- **Customizable**: Support for custom status codes and messages

## Basic Usage

### Success Responses

```java
// Basic success response
return RestResponseBuilder.success(productData, "Product retrieved successfully");

// Success with default message
return RestResponseBuilder.success(productData);

// Created response (HTTP 201)
return RestResponseBuilder.created(newProduct, "Product created successfully");

// No content response (HTTP 204)
return RestResponseBuilder.noContent("Product deleted successfully");
```

### Error Responses

```java
// Bad request (HTTP 400)
return RestResponseBuilder.badRequest("Invalid product data");

// Not found (HTTP 404)
return RestResponseBuilder.notFound("Product not found");

// Internal server error (HTTP 500)
return RestResponseBuilder.internalServerError("Database connection failed");

// Validation error (HTTP 422)
return RestResponseBuilder.validationError("Price must be positive");

// Unauthorized (HTTP 401)
return RestResponseBuilder.unauthorized("Authentication required");

// Forbidden (HTTP 403)
return RestResponseBuilder.forbidden("Access denied");

// Conflict (HTTP 409)
return RestResponseBuilder.conflict("Product already exists");
```

### Custom Responses

```java
// Custom status and message
return RestResponseBuilder.custom(data, "Custom message", HttpStatus.ACCEPTED);
```

## Controller Example

Here's how to use the RestResponseBuilder in your controllers:

```java
@RestController
@RequestMapping("/products")
public class ProductController {

    private final ProductService productService;

    @PostMapping
    public ResponseEntity<ResponseStructure<ProductResponse>> createProduct(
            @Valid @RequestBody ProductRequest request) {
        try {
            ProductResponse product = productService.createProduct(request);
            return RestResponseBuilder.created(product, "Product created successfully");
        } catch (IllegalArgumentException e) {
            return RestResponseBuilder.badRequest(e.getMessage());
        } catch (Exception e) {
            return RestResponseBuilder.internalServerError("Failed to create product");
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseStructure<ProductResponse>> getProduct(@PathVariable Long id) {
        try {
            ProductResponse product = productService.getProductById(id);
            return RestResponseBuilder.success(product, "Product retrieved successfully");
        } catch (IllegalArgumentException e) {
            return RestResponseBuilder.notFound(e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseStructure<Void>> deleteProduct(@PathVariable Long id) {
        try {
            productService.deleteProduct(id);
            return RestResponseBuilder.noContent("Product deleted successfully");
        } catch (IllegalArgumentException e) {
            return RestResponseBuilder.notFound(e.getMessage());
        }
    }
}
```

## Global Exception Handler Integration

You can also use RestResponseBuilder in global exception handlers:

```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ResponseStructure<Map<String, String>>> handleValidationErrors(
            MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        return RestResponseBuilder.custom(errors, "Validation failed", HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ResponseStructure<Void>> handleIllegalArgument(IllegalArgumentException ex) {
        return RestResponseBuilder.badRequest(ex.getMessage());
    }
}
```

## Response Structure

All responses follow this consistent structure:

```json
{
  "status": 200,
  "message": "Operation completed successfully",
  "data": {
    "id": 1,
    "name": "Product Name",
    "price": 99.99,
    "category": "Electronics",
    "description": "Product description",
    "createdAt": "2023-12-15T10:30:00"
  }
}
```

For error responses, the `data` field will be `null`:

```json
{
  "status": 404,
  "message": "Product not found",
  "data": null
}
```

## Available Methods

### Success Methods
- `success(T data, String message)` - HTTP 200 with custom message
- `success(T data)` - HTTP 200 with default message
- `created(T data, String message)` - HTTP 201 with custom message
- `created(T data)` - HTTP 201 with default message
- `noContent(String message)` - HTTP 204 with custom message
- `noContent()` - HTTP 204 with default message

### Error Methods
- `badRequest(String message)` - HTTP 400
- `unauthorized(String message)` - HTTP 401
- `unauthorized()` - HTTP 401 with default message
- `forbidden(String message)` - HTTP 403
- `forbidden()` - HTTP 403 with default message
- `notFound(String message)` - HTTP 404
- `notFound()` - HTTP 404 with default message
- `conflict(String message)` - HTTP 409
- `validationError(String message)` - HTTP 422
- `internalServerError(String message)` - HTTP 500
- `internalServerError()` - HTTP 500 with default message

### Custom Method
- `custom(T data, String message, HttpStatus status)` - Custom status and message

## Benefits

1. **Consistency**: All API responses follow the same structure
2. **Maintainability**: Centralized response creation logic
3. **Readability**: Clear, expressive method names
4. **Type Safety**: Generic support prevents type errors
5. **Error Handling**: Standardized error response format
6. **HTTP Compliance**: Proper HTTP status codes
7. **Documentation**: Self-documenting code through method names

## Testing

The RestResponseBuilder includes comprehensive unit tests. Run them with:

```bash
mvn test -Dtest=RestResponseBuilderTest
```

## Integration with Existing Code

The RestResponseBuilder is designed to work with your existing `ResponseStructure<T>` class without requiring any changes to your current data models or DTOs. Simply replace your manual `ResponseEntity` creation with the appropriate RestResponseBuilder method calls.
