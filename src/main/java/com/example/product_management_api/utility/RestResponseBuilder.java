package com.example.product_management_api.utility;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * A fluent builder for creating standardized REST API responses using ResponseStructure.
 * Provides convenient methods for building success and error responses with proper HTTP status codes.
 * 
 * <AUTHOR> Management API Team
 */
public class RestResponseBuilder {

    /**
     * Creates a successful response with HTTP 200 OK status.
     * 
     * @param <T> the type of data being returned
     * @param data the response data
     * @param message the success message
     * @return ResponseEntity with ResponseStructure containing the data
     */
    public static <T> ResponseEntity<ResponseStructure<T>> success(T data, String message) {
        ResponseStructure<T> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.OK.value());
        response.setMessage(message);
        response.setData(data);
        return ResponseEntity.ok(response);
    }

    /**
     * Creates a successful response with HTTP 200 OK status and default message.
     * 
     * @param <T> the type of data being returned
     * @param data the response data
     * @return ResponseEntity with ResponseStructure containing the data
     */
    public static <T> ResponseEntity<ResponseStructure<T>> success(T data) {
        return success(data, "Operation completed successfully");
    }

    /**
     * Creates a successful response with HTTP 201 CREATED status.
     * 
     * @param <T> the type of data being returned
     * @param data the created resource data
     * @param message the success message
     * @return ResponseEntity with ResponseStructure containing the created data
     */
    public static <T> ResponseEntity<ResponseStructure<T>> created(T data, String message) {
        ResponseStructure<T> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.CREATED.value());
        response.setMessage(message);
        response.setData(data);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Creates a successful response with HTTP 201 CREATED status and default message.
     * 
     * @param <T> the type of data being returned
     * @param data the created resource data
     * @return ResponseEntity with ResponseStructure containing the created data
     */
    public static <T> ResponseEntity<ResponseStructure<T>> created(T data) {
        return created(data, "Resource created successfully");
    }

    /**
     * Creates a successful response with HTTP 204 NO CONTENT status.
     * 
     * @param message the success message
     * @return ResponseEntity with ResponseStructure containing no data
     */
    public static ResponseEntity<ResponseStructure<Void>> noContent(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.NO_CONTENT.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(response);
    }

    /**
     * Creates a successful response with HTTP 204 NO CONTENT status and default message.
     * 
     * @return ResponseEntity with ResponseStructure containing no data
     */
    public static ResponseEntity<ResponseStructure<Void>> noContent() {
        return noContent("Operation completed successfully");
    }

    /**
     * Creates an error response with HTTP 400 BAD REQUEST status.
     * 
     * @param message the error message
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> badRequest(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * Creates an error response with HTTP 404 NOT FOUND status.
     * 
     * @param message the error message
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> notFound(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.NOT_FOUND.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * Creates an error response with HTTP 404 NOT FOUND status and default message.
     * 
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> notFound() {
        return notFound("Resource not found");
    }

    /**
     * Creates an error response with HTTP 409 CONFLICT status.
     * 
     * @param message the error message
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> conflict(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.CONFLICT.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    /**
     * Creates an error response with HTTP 500 INTERNAL SERVER ERROR status.
     * 
     * @param message the error message
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> internalServerError(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * Creates an error response with HTTP 500 INTERNAL SERVER ERROR status and default message.
     * 
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> internalServerError() {
        return internalServerError("An internal server error occurred");
    }

    /**
     * Creates an error response with HTTP 422 UNPROCESSABLE ENTITY status for validation errors.
     * 
     * @param message the validation error message
     * @return ResponseEntity with ResponseStructure containing validation error details
     */
    public static ResponseEntity<ResponseStructure<Void>> validationError(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(response);
    }

    /**
     * Creates an error response with HTTP 401 UNAUTHORIZED status.
     * 
     * @param message the error message
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> unauthorized(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    /**
     * Creates an error response with HTTP 401 UNAUTHORIZED status and default message.
     * 
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> unauthorized() {
        return unauthorized("Authentication required");
    }

    /**
     * Creates an error response with HTTP 403 FORBIDDEN status.
     * 
     * @param message the error message
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> forbidden(String message) {
        ResponseStructure<Void> response = new ResponseStructure<>();
        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.setMessage(message);
        response.setData(null);
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    /**
     * Creates an error response with HTTP 403 FORBIDDEN status and default message.
     * 
     * @return ResponseEntity with ResponseStructure containing error details
     */
    public static ResponseEntity<ResponseStructure<Void>> forbidden() {
        return forbidden("Access denied");
    }

    /**
     * Creates a custom response with specified HTTP status.
     * 
     * @param <T> the type of data being returned
     * @param data the response data
     * @param message the response message
     * @param status the HTTP status
     * @return ResponseEntity with ResponseStructure containing the data
     */
    public static <T> ResponseEntity<ResponseStructure<T>> custom(T data, String message, HttpStatus status) {
        ResponseStructure<T> response = new ResponseStructure<>();
        response.setStatus(status.value());
        response.setMessage(message);
        response.setData(data);
        return ResponseEntity.status(status).body(response);
    }
}
