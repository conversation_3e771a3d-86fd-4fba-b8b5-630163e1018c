package com.example.product_management_api.exception;

import com.example.product_management_api.utility.ResponseStructure;
import com.example.product_management_api.utility.RestResponseBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for the Product Management API.
 * Provides centralized exception handling and standardized error responses.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * Handles validation errors from @Valid annotations.
     * 
     * @param ex the MethodArgumentNotValidException
     * @return ResponseEntity with validation error details
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ResponseStructure<Map<String, String>>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        return RestResponseBuilder.custom(errors, "Validation failed", 
                org.springframework.http.HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles IllegalArgumentException.
     * 
     * @param ex the IllegalArgumentException
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ResponseStructure<Void>> handleIllegalArgumentException(IllegalArgumentException ex) {
        return RestResponseBuilder.badRequest(ex.getMessage());
    }

    /**
     * Handles generic exceptions.
     * 
     * @param ex the Exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResponseStructure<Void>> handleGenericException(Exception ex) {
        return RestResponseBuilder.internalServerError("An unexpected error occurred: " + ex.getMessage());
    }
}
