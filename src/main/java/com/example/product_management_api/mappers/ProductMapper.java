package com.example.product_management_api.mappers;

import com.example.product_management_api.dtos.request.ProductRequest;
import com.example.product_management_api.dtos.response.ProductResponse;
import com.example.product_management_api.entity.Product;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ProductMapper {


    Product toEntity(ProductRequest request);

    ProductResponse toResponse(Product product);

}
