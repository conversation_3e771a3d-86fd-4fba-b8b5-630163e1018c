package com.example.product_management_api.utility;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for RestResponseBuilder.
 */
class RestResponseBuilderTest {

    @Test
    void testSuccessResponse() {
        String data = "Test Data";
        String message = "Success message";
        
        ResponseEntity<ResponseStructure<String>> response = RestResponseBuilder.success(data, message);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertEquals(data, response.getBody().getData());
    }

    @Test
    void testSuccessResponseWithDefaultMessage() {
        String data = "Test Data";
        
        ResponseEntity<ResponseStructure<String>> response = RestResponseBuilder.success(data);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getBody().getStatus());
        assertEquals("Operation completed successfully", response.getBody().getMessage());
        assertEquals(data, response.getBody().getData());
    }

    @Test
    void testCreatedResponse() {
        String data = "Created Data";
        String message = "Created successfully";
        
        ResponseEntity<ResponseStructure<String>> response = RestResponseBuilder.created(data, message);
        
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(201, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertEquals(data, response.getBody().getData());
    }

    @Test
    void testBadRequestResponse() {
        String message = "Bad request message";
        
        ResponseEntity<ResponseStructure<Void>> response = RestResponseBuilder.badRequest(message);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(400, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    @Test
    void testNotFoundResponse() {
        String message = "Resource not found";
        
        ResponseEntity<ResponseStructure<Void>> response = RestResponseBuilder.notFound(message);
        
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(404, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    @Test
    void testNotFoundResponseWithDefaultMessage() {
        ResponseEntity<ResponseStructure<Void>> response = RestResponseBuilder.notFound();
        
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(404, response.getBody().getStatus());
        assertEquals("Resource not found", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    @Test
    void testInternalServerErrorResponse() {
        String message = "Internal server error";
        
        ResponseEntity<ResponseStructure<Void>> response = RestResponseBuilder.internalServerError(message);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(500, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    @Test
    void testCustomResponse() {
        String data = "Custom Data";
        String message = "Custom message";
        HttpStatus status = HttpStatus.ACCEPTED;
        
        ResponseEntity<ResponseStructure<String>> response = RestResponseBuilder.custom(data, message, status);
        
        assertEquals(HttpStatus.ACCEPTED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(202, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertEquals(data, response.getBody().getData());
    }

    @Test
    void testNoContentResponse() {
        String message = "No content message";
        
        ResponseEntity<ResponseStructure<Void>> response = RestResponseBuilder.noContent(message);
        
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(204, response.getBody().getStatus());
        assertEquals(message, response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }
}
